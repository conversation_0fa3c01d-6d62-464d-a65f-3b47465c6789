import { PrismaClient } from "@prisma/client"

const prisma = new PrismaClient()

export interface AdminOverrideConfig {
  systemPrompt?: string
  llmProvider?: string
  llmModel?: string
  encryptedApiKey?: string
  widgetConfig?: any
  simpleKbText?: string
}

export interface OverrideResult {
  hasOverrides: boolean
  overrideTypes: string[]
  effectiveConfig: {
    systemPrompt?: string | null
    llmProvider: string
    llmModel: string
    apiKey?: string | null
    widgetConfig: any
    knowledgeBase?: string | null
  }
}

/**
 * Analyzes and applies admin overrides for a chatbot
 * This function implements the complete hierarchy:
 * 1. Admin System Prompt (highest priority)
 * 2. Admin LLM Config (overrides user BYOK)
 * 3. Admin Widget Config (overrides user settings)
 * 4. Admin Knowledge Base (supplements or replaces user KB)
 */
export async function analyzeAdminOverrides(chatbotId: string): Promise<OverrideResult> {
  const chatbot = await prisma.chatbot.findUnique({
    where: { id: chatbotId },
    include: {
      user: {
        include: {
          subscription: {
            include: {
              plan: true
            }
          }
        }
      }
    }
  })

  if (!chatbot) {
    throw new Error("Chatbot not found")
  }

  const overrideTypes: string[] = []
  let hasOverrides = false

  // Check for system prompt override
  const hasSystemPromptOverride = !!chatbot.systemPrompt
  if (hasSystemPromptOverride) {
    overrideTypes.push("System Prompt")
    hasOverrides = true
  }

  // Check for LLM config override
  const hasLLMOverride = chatbot.llmProvider !== "gemini" || 
                        chatbot.llmModel !== "gemini-pro" || 
                        !!chatbot.encryptedLlmApiKey
  if (hasLLMOverride) {
    overrideTypes.push("LLM Configuration")
    hasOverrides = true
  }

  // Check for widget config override
  const defaultWidgetConfig = {
    primaryColor: "#3B82F6",
    welcomeMessage: "Hello! How can I help you today?",
    position: "bottom-right",
    showBranding: true
  }
  
  const hasWidgetOverride = chatbot.widgetConfig && 
    JSON.stringify(chatbot.widgetConfig) !== JSON.stringify(defaultWidgetConfig)
  if (hasWidgetOverride) {
    overrideTypes.push("Widget Configuration")
    hasOverrides = true
  }

  // Check for knowledge base override
  const hasKBOverride = !!chatbot.simpleKbText && hasSystemPromptOverride
  if (hasKBOverride) {
    overrideTypes.push("Knowledge Base")
    hasOverrides = true
  }

  // Build effective configuration
  const effectiveConfig = {
    systemPrompt: chatbot.systemPrompt,
    llmProvider: chatbot.llmProvider,
    llmModel: chatbot.llmModel,
    apiKey: chatbot.encryptedLlmApiKey,
    widgetConfig: chatbot.widgetConfig || defaultWidgetConfig,
    knowledgeBase: hasKBOverride ? chatbot.simpleKbText : undefined
  }

  return {
    hasOverrides,
    overrideTypes,
    effectiveConfig
  }
}

/**
 * Validates admin override permissions
 */
export async function validateAdminOverridePermissions(adminUserId: string): Promise<boolean> {
  const admin = await prisma.user.findUnique({
    where: { id: adminUserId }
  })

  return admin?.role === "ADMIN"
}

/**
 * Logs admin override actions for audit trail
 */
export async function logAdminOverride(
  adminUserId: string,
  chatbotId: string,
  overrideType: string,
  action: "CREATE" | "UPDATE" | "DELETE",
  details?: any
): Promise<void> {
  try {
    // In a production system, you'd want a proper audit log table
    console.log(`ADMIN OVERRIDE LOG: ${action} ${overrideType} for chatbot ${chatbotId} by admin ${adminUserId}`, details)
    
    // For now, we'll just log to console, but in production you'd save to database:
    // await prisma.adminAuditLog.create({
    //   data: {
    //     adminUserId,
    //     chatbotId,
    //     action,
    //     overrideType,
    //     details: JSON.stringify(details),
    //     timestamp: new Date()
    //   }
    // })
  } catch (error) {
    console.error("Failed to log admin override:", error)
  }
}

/**
 * Gets override summary for admin dashboard
 */
export async function getOverrideSummary(): Promise<{
  totalChatbots: number
  chatbotsWithOverrides: number
  overrideBreakdown: Record<string, number>
}> {
  const allChatbots = await prisma.chatbot.findMany({
    select: {
      id: true,
      systemPrompt: true,
      llmProvider: true,
      llmModel: true,
      encryptedLlmApiKey: true,
      widgetConfig: true,
      simpleKbText: true
    }
  })

  const overrideBreakdown = {
    "System Prompt": 0,
    "LLM Configuration": 0,
    "Widget Configuration": 0,
    "Knowledge Base": 0
  }

  let chatbotsWithOverrides = 0

  for (const chatbot of allChatbots) {
    let hasAnyOverride = false

    // System prompt override
    if (chatbot.systemPrompt) {
      overrideBreakdown["System Prompt"]++
      hasAnyOverride = true
    }

    // LLM override
    if (chatbot.llmProvider !== "gemini" || chatbot.llmModel !== "gemini-pro" || chatbot.encryptedLlmApiKey) {
      overrideBreakdown["LLM Configuration"]++
      hasAnyOverride = true
    }

    // Widget override
    const defaultWidgetConfig = {
      primaryColor: "#3B82F6",
      welcomeMessage: "Hello! How can I help you today?",
      position: "bottom-right",
      showBranding: true
    }
    if (chatbot.widgetConfig && JSON.stringify(chatbot.widgetConfig) !== JSON.stringify(defaultWidgetConfig)) {
      overrideBreakdown["Widget Configuration"]++
      hasAnyOverride = true
    }

    // Knowledge base override
    if (chatbot.simpleKbText && chatbot.systemPrompt) {
      overrideBreakdown["Knowledge Base"]++
      hasAnyOverride = true
    }

    if (hasAnyOverride) {
      chatbotsWithOverrides++
    }
  }

  return {
    totalChatbots: allChatbots.length,
    chatbotsWithOverrides,
    overrideBreakdown
  }
}

/**
 * Safely removes admin overrides
 */
export async function removeAdminOverrides(
  chatbotId: string,
  overrideTypes: string[],
  adminUserId: string
): Promise<void> {
  const updateData: any = {}

  if (overrideTypes.includes("System Prompt")) {
    updateData.systemPrompt = null
  }

  if (overrideTypes.includes("LLM Configuration")) {
    updateData.llmProvider = "gemini"
    updateData.llmModel = "gemini-pro"
    updateData.encryptedLlmApiKey = null
  }

  if (overrideTypes.includes("Widget Configuration")) {
    updateData.widgetConfig = {
      primaryColor: "#3B82F6",
      welcomeMessage: "Hello! How can I help you today?",
      position: "bottom-right",
      showBranding: true
    }
  }

  if (overrideTypes.includes("Knowledge Base")) {
    updateData.simpleKbText = null
  }

  await prisma.chatbot.update({
    where: { id: chatbotId },
    data: updateData
  })

  // Log the removal
  await logAdminOverride(adminUserId, chatbotId, overrideTypes.join(", "), "DELETE", updateData)
}
